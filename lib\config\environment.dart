/// Environment configuration for PerkTracker
/// 
/// This file manages environment-specific settings for development,
/// staging, and production environments.

enum Environment {
  development,
  staging,
  production,
}

class EnvironmentConfig {
  static const String _environmentKey = 'ENVIRONMENT';
  static const String _apiUrlKey = 'API_URL';
  static const String _enableLoggingKey = 'ENABLE_LOGGING';
  static const String _enableAnalyticsKey = 'ENABLE_ANALYTICS';

  /// Current environment
  static Environment get environment {
    const envString = String.fromEnvironment(_environmentKey, defaultValue: 'production');
    switch (envString.toLowerCase()) {
      case 'development':
      case 'dev':
        return Environment.development;
      case 'staging':
      case 'stage':
        return Environment.staging;
      case 'production':
      case 'prod':
      default:
        return Environment.production;
    }
  }

  /// Whether this is a development build
  static bool get isDevelopment => environment == Environment.development;

  /// Whether this is a staging build
  static bool get isStaging => environment == Environment.staging;

  /// Whether this is a production build
  static bool get isProduction => environment == Environment.production;

  /// API base URL for the current environment
  static String get apiUrl {
    const customUrl = String.fromEnvironment(_apiUrlKey);
    if (customUrl.isNotEmpty) return customUrl;

    switch (environment) {
      case Environment.development:
        return 'http://localhost:5001/perktracker-4c5be/us-central1';
      case Environment.staging:
        return 'https://us-central1-perktracker-staging.cloudfunctions.net';
      case Environment.production:
        return 'https://us-central1-perktracker-4c5be.cloudfunctions.net';
    }
  }

  /// Whether logging is enabled
  static bool get enableLogging {
    const customValue = String.fromEnvironment(_enableLoggingKey);
    if (customValue.isNotEmpty) {
      return customValue.toLowerCase() == 'true';
    }
    return !isProduction;
  }

  /// Whether analytics is enabled
  static bool get enableAnalytics {
    const customValue = String.fromEnvironment(_enableAnalyticsKey);
    if (customValue.isNotEmpty) {
      return customValue.toLowerCase() == 'true';
    }
    return isProduction || isStaging;
  }

  /// App name for the current environment
  static String get appName {
    switch (environment) {
      case Environment.development:
        return 'PerkTracker (Dev)';
      case Environment.staging:
        return 'PerkTracker (Staging)';
      case Environment.production:
        return 'PerkTracker';
    }
  }

  /// App suffix for the current environment
  static String get appSuffix {
    switch (environment) {
      case Environment.development:
        return '.dev';
      case Environment.staging:
        return '.staging';
      case Environment.production:
        return '';
    }
  }

  /// Firebase project ID for the current environment
  static String get firebaseProjectId {
    switch (environment) {
      case Environment.development:
        return 'perktracker-4c5be'; // Use same as prod for dev
      case Environment.staging:
        return 'perktracker-staging';
      case Environment.production:
        return 'perktracker-4c5be';
    }
  }

  /// Whether to use Firebase emulators
  static bool get useFirebaseEmulators {
    const useEmulators = String.fromEnvironment('USE_FIREBASE_EMULATOR');
    return isDevelopment && useEmulators.toLowerCase() == 'true';
  }

  /// Debug information about current environment
  static Map<String, dynamic> get debugInfo => {
    'environment': environment.name,
    'isDevelopment': isDevelopment,
    'isStaging': isStaging,
    'isProduction': isProduction,
    'apiUrl': apiUrl,
    'enableLogging': enableLogging,
    'enableAnalytics': enableAnalytics,
    'appName': appName,
    'firebaseProjectId': firebaseProjectId,
    'useFirebaseEmulators': useFirebaseEmulators,
  };

  /// Print environment configuration (development only)
  static void printConfig() {
    if (!enableLogging) return;
    
    print('🔧 Environment Configuration:');
    debugInfo.forEach((key, value) {
      print('   $key: $value');
    });
  }
}
