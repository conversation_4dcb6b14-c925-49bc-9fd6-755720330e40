import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Screen for finding the best card for a purchase
class WhichCardScreen extends ConsumerStatefulWidget {
  const WhichCardScreen({super.key});

  @override
  ConsumerState<WhichCardScreen> createState() => _WhichCardScreenState();
}

class _WhichCardScreenState extends ConsumerState<WhichCardScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Which Card?'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search, size: 64),
            <PERSON><PERSON><PERSON><PERSON>(height: 16),
            Text('Which Card Screen'),
            Text('Coming soon...'),
          ],
        ),
      ),
    );
  }
}
