// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_card.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreditCard _$CreditCardFromJson(Map<String, dynamic> json) => CreditCard(
  id: json['id'] as String,
  name: json['name'] as String,
  issuer: json['issuer'] as String,
  rewards:
      (json['rewards'] as List<dynamic>)
          .map((e) => Reward.fromJson(e as Map<String, dynamic>))
          .toList(),
  notes: json['notes'] as String? ?? '',
  isActive: json['isActive'] as bool? ?? true,
  dateAdded: DateTime.parse(json['dateAdded'] as String),
  lastFourDigits: json['lastFourDigits'] as String?,
  annualFee: (json['annualFee'] as num?)?.toDouble(),
  defaultRewardRate: (json['defaultRewardRate'] as num?)?.toDouble() ?? 1.0,
  defaultIsPercentage: json['defaultIsPercentage'] as bool? ?? true,
);

Map<String, dynamic> _$CreditCardToJson(CreditCard instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'issuer': instance.issuer,
      'rewards': instance.rewards,
      'notes': instance.notes,
      'isActive': instance.isActive,
      'dateAdded': instance.dateAdded.toIso8601String(),
      'lastFourDigits': instance.lastFourDigits,
      'annualFee': instance.annualFee,
      'defaultRewardRate': instance.defaultRewardRate,
      'defaultIsPercentage': instance.defaultIsPercentage,
    };
