import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'reward.dart';
import '../utils/categories.dart';

part 'credit_card.g.dart';

/// Represents a credit card with its reward structure
@JsonSerializable()
class CreditCard extends Equatable {
  /// Unique identifier for the card
  final String id;

  /// Display name of the card
  final String name;

  /// Card issuer (Chase, Amex, etc.)
  final String issuer;

  /// List of rewards offered by this card
  final List<Reward> rewards;

  /// Additional notes about the card
  final String notes;

  /// Whether this card is currently active/in use
  final bool isActive;

  /// Date when the card was added
  final DateTime dateAdded;

  /// Last 4 digits of the card (optional, for identification)
  final String? lastFourDigits;

  /// Annual fee of the card
  final double? annualFee;

  /// Default reward rate for categories not specified
  final double defaultRewardRate;

  /// Whether default rate is percentage or multiplier
  final bool defaultIsPercentage;

  const CreditCard({
    required this.id,
    required this.name,
    required this.issuer,
    required this.rewards,
    this.notes = '',
    this.isActive = true,
    required this.dateAdded,
    this.lastFourDigits,
    this.annualFee,
    this.defaultRewardRate = 1.0,
    this.defaultIsPercentage = true,
  });

  /// Create a new credit card with generated ID
  factory CreditCard.create({
    required String name,
    required String issuer,
    List<Reward>? rewards,
    String notes = '',
    bool isActive = true,
    String? lastFourDigits,
    double? annualFee,
    double defaultRewardRate = 1.0,
    bool defaultIsPercentage = true,
  }) {
    return CreditCard(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      issuer: issuer,
      rewards: rewards ?? [],
      notes: notes,
      isActive: isActive,
      dateAdded: DateTime.now(),
      lastFourDigits: lastFourDigits,
      annualFee: annualFee,
      defaultRewardRate: defaultRewardRate,
      defaultIsPercentage: defaultIsPercentage,
    );
  }

  /// Get all active rewards for this card
  List<Reward> get activeRewards {
    return rewards.where((reward) => reward.isActive).toList();
  }

  /// Get the best reward rate for a specific category
  Reward? getBestRewardForCategory(RewardCategory category) {
    final applicableRewards = activeRewards
        .where((reward) => reward.appliesToCategory(category))
        .toList();

    if (applicableRewards.isEmpty) {
      return null;
    }

    // Sort by rate (highest first)
    applicableRewards.sort((a, b) => b.rate.compareTo(a.rate));
    return applicableRewards.first;
  }

  /// Get effective reward rate for a category (including default)
  double getEffectiveRewardRate(RewardCategory category) {
    final bestReward = getBestRewardForCategory(category);
    return bestReward?.rate ?? defaultRewardRate;
  }

  /// Get formatted effective reward rate for display
  String getFormattedRewardRate(RewardCategory category) {
    final bestReward = getBestRewardForCategory(category);
    if (bestReward != null) {
      return bestReward.formattedRate;
    }

    // Use default rate
    if (defaultIsPercentage) {
      return '${(defaultRewardRate * 100).toStringAsFixed(defaultRewardRate == defaultRewardRate.roundToDouble() ? 0 : 1)}%';
    } else {
      return '${defaultRewardRate.toStringAsFixed(defaultRewardRate == defaultRewardRate.roundToDouble() ? 0 : 1)}x';
    }
  }

  /// Get all categories this card offers rewards for
  Set<RewardCategory> get rewardCategories {
    return activeRewards.map((reward) => reward.category).toSet();
  }

  /// Get quarterly rewards that are currently active
  List<Reward> get currentQuarterlyRewards {
    return activeRewards
        .where((reward) => reward.type == RewardType.quarterly)
        .toList();
  }

  /// Get promotional rewards that are currently active
  List<Reward> get currentPromotionalRewards {
    return activeRewards
        .where((reward) => reward.type == RewardType.promo)
        .toList();
  }

  /// Get static rewards (always active)
  List<Reward> get staticRewards {
    return rewards.where((reward) => reward.type == RewardType.static).toList();
  }

  /// Check if this card has any quarterly rotating categories
  bool get hasQuarterlyRewards {
    return rewards.any((reward) => reward.type == RewardType.quarterly);
  }

  /// Check if this card has any active promotional offers
  bool get hasActivePromotions {
    return currentPromotionalRewards.isNotEmpty;
  }

  /// Get display name with last 4 digits if available
  String get displayName {
    if (lastFourDigits != null) {
      return '$name (...$lastFourDigits)';
    }
    return name;
  }

  /// Get formatted annual fee for display
  String get formattedAnnualFee {
    if (annualFee == null || annualFee == 0) {
      return 'No annual fee';
    }
    return '\$${annualFee!.toStringAsFixed(0)} annual fee';
  }

  /// Add a new reward to this card
  CreditCard addReward(Reward reward) {
    final updatedRewards = List<Reward>.from(rewards)..add(reward);
    return copyWith(rewards: updatedRewards);
  }

  /// Remove a reward from this card
  CreditCard removeReward(Reward reward) {
    final updatedRewards = List<Reward>.from(rewards)..remove(reward);
    return copyWith(rewards: updatedRewards);
  }

  /// Update a reward on this card
  CreditCard updateReward(Reward oldReward, Reward newReward) {
    final updatedRewards = rewards.map((reward) {
      return reward == oldReward ? newReward : reward;
    }).toList();
    return copyWith(rewards: updatedRewards);
  }

  /// Create a copy with updated values
  CreditCard copyWith({
    String? id,
    String? name,
    String? issuer,
    List<Reward>? rewards,
    String? notes,
    bool? isActive,
    DateTime? dateAdded,
    String? lastFourDigits,
    double? annualFee,
    double? defaultRewardRate,
    bool? defaultIsPercentage,
  }) {
    return CreditCard(
      id: id ?? this.id,
      name: name ?? this.name,
      issuer: issuer ?? this.issuer,
      rewards: rewards ?? this.rewards,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      dateAdded: dateAdded ?? this.dateAdded,
      lastFourDigits: lastFourDigits ?? this.lastFourDigits,
      annualFee: annualFee ?? this.annualFee,
      defaultRewardRate: defaultRewardRate ?? this.defaultRewardRate,
      defaultIsPercentage: defaultIsPercentage ?? this.defaultIsPercentage,
    );
  }

  /// JSON serialization
  factory CreditCard.fromJson(Map<String, dynamic> json) =>
      _$CreditCardFromJson(json);
  Map<String, dynamic> toJson() => _$CreditCardToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        issuer,
        rewards,
        notes,
        isActive,
        dateAdded,
        lastFourDigits,
        annualFee,
        defaultRewardRate,
        defaultIsPercentage,
      ];

  @override
  String toString() {
    return 'CreditCard(name: $name, issuer: $issuer, rewards: ${rewards.length}, active: $isActive)';
  }
}
