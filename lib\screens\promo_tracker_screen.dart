import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Screen for tracking promotional offers
class PromoTrackerScreen extends ConsumerStatefulWidget {
  const PromoTrackerScreen({super.key});

  @override
  ConsumerState<PromoTrackerScreen> createState() => _PromoTrackerScreenState();
}

class _PromoTrackerScreenState extends ConsumerState<PromoTrackerScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Promo Tracker'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.local_offer, size: 64),
            Sized<PERSON><PERSON>(height: 16),
            Text('Promo Tracker Screen'),
            Text('Coming soon...'),
          ],
        ),
      ),
    );
  }
}
