# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Firebase configuration and sensitive files
firebase-debug.log
.firebase/
.firebaserc
firebase.json
firestore.rules
firestore.indexes.json
firestore.indexes.new.json
android/app/google-services.json
ios/Runner/GoogleService-Info.plist
# lib/firebase_options.dart - Temporarily allow for CI/CD

# Documentation with sensitive setup information
docs/
*.md
!README.md

# Memory bank and collaboration logs
.memory-bank/

# Environment and local configuration
.env
.env.local
.env.*.local
local.properties

# IDE and editor files
.vscode/
*.code-workspace

# Build and deployment artifacts
build/
.dart_tool/cache/
coverage/

# Scripts with potential sensitive information
scripts/
