import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import '../utils/categories.dart';
import '../utils/dates.dart' as date_utils;

part 'reward.g.dart';

/// Types of rewards offered by credit cards
enum RewardType {
  @JsonValue('static')
  static('Static'),
  @JsonValue('quarterly')
  quarterly('Quarterly'),
  @JsonValue('promo')
  promo('Promotional');

  const RewardType(this.displayName);
  final String displayName;
}

/// Represents a credit card reward for a specific category
@JsonSerializable()
class Reward extends Equatable {
  /// The category this reward applies to
  final RewardCategory category;
  
  /// The reward rate (e.g., 0.05 for 5% back, 2.0 for 2x points)
  final double rate;
  
  /// The type of reward (static, quarterly, promotional)
  final RewardType type;
  
  /// Start date for promotional/quarterly rewards
  final DateTime? startDate;
  
  /// End date for promotional/quarterly rewards
  final DateTime? endDate;
  
  /// Optional spending cap for the reward
  final double? spendingCap;
  
  /// Whether the reward rate is in percentage (true) or multiplier (false)
  final bool isPercentage;
  
  /// Additional notes about the reward
  final String? notes;

  const Reward({
    required this.category,
    required this.rate,
    required this.type,
    this.startDate,
    this.endDate,
    this.spendingCap,
    this.isPercentage = true,
    this.notes,
  });

  /// Create a static reward (no expiration)
  factory Reward.static({
    required RewardCategory category,
    required double rate,
    bool isPercentage = true,
    double? spendingCap,
    String? notes,
  }) {
    return Reward(
      category: category,
      rate: rate,
      type: RewardType.static,
      isPercentage: isPercentage,
      spendingCap: spendingCap,
      notes: notes,
    );
  }

  /// Create a quarterly reward
  factory Reward.quarterly({
    required RewardCategory category,
    required double rate,
    required int year,
    required int quarter,
    bool isPercentage = true,
    double? spendingCap,
    String? notes,
  }) {
    return Reward(
      category: category,
      rate: rate,
      type: RewardType.quarterly,
      startDate: date_utils.DateUtils.getQuarterStart(year, quarter),
      endDate: date_utils.DateUtils.getQuarterEnd(year, quarter),
      isPercentage: isPercentage,
      spendingCap: spendingCap,
      notes: notes,
    );
  }

  /// Create a promotional reward
  factory Reward.promo({
    required RewardCategory category,
    required double rate,
    DateTime? startDate,
    DateTime? endDate,
    bool isPercentage = true,
    double? spendingCap,
    String? notes,
  }) {
    return Reward(
      category: category,
      rate: rate,
      type: RewardType.promo,
      startDate: startDate,
      endDate: endDate,
      isPercentage: isPercentage,
      spendingCap: spendingCap,
      notes: notes,
    );
  }

  /// Check if this reward is currently active
  bool get isActive {
    return date_utils.DateUtils.isPromoActive(startDate, endDate);
  }

  /// Get days remaining for this reward (null if no expiration)
  int? get daysRemaining {
    return date_utils.DateUtils.getDaysRemainingForPromo(endDate);
  }

  /// Get formatted rate for display
  String get formattedRate {
    if (isPercentage) {
      return '${(rate * 100).toStringAsFixed(rate == rate.roundToDouble() ? 0 : 1)}%';
    } else {
      return '${rate.toStringAsFixed(rate == rate.roundToDouble() ? 0 : 1)}x';
    }
  }

  /// Get formatted spending cap for display
  String? get formattedSpendingCap {
    if (spendingCap == null) return null;
    return '\$${spendingCap!.toStringAsFixed(0)}';
  }

  /// Get date range string for display
  String get dateRangeDisplay {
    return date_utils.DateUtils.formatDateRange(startDate, endDate);
  }

  /// Check if this reward applies to a specific category
  bool appliesToCategory(RewardCategory targetCategory) {
    return category == targetCategory;
  }

  /// Compare rewards by rate (higher is better)
  int compareByRate(Reward other) {
    return other.rate.compareTo(rate);
  }

  /// Create a copy with updated values
  Reward copyWith({
    RewardCategory? category,
    double? rate,
    RewardType? type,
    DateTime? startDate,
    DateTime? endDate,
    double? spendingCap,
    bool? isPercentage,
    String? notes,
  }) {
    return Reward(
      category: category ?? this.category,
      rate: rate ?? this.rate,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      spendingCap: spendingCap ?? this.spendingCap,
      isPercentage: isPercentage ?? this.isPercentage,
      notes: notes ?? this.notes,
    );
  }

  /// JSON serialization
  factory Reward.fromJson(Map<String, dynamic> json) => _$RewardFromJson(json);
  Map<String, dynamic> toJson() => _$RewardToJson(this);

  @override
  List<Object?> get props => [
        category,
        rate,
        type,
        startDate,
        endDate,
        spendingCap,
        isPercentage,
        notes,
      ];

  @override
  String toString() {
    return 'Reward(category: $category, rate: $formattedRate, type: $type, active: $isActive)';
  }
}
