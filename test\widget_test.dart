// PerkTracker Widget Tests
//
// Tests for the PerkTracker credit card rewards optimization app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:perk_tracker/main.dart';

void main() {
  testWidgets('PerkTracker app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: PerkTrackerApp()));

    // Wait for Firebase initialization and auth state
    await tester.pumpAndSettle();

    // Verify that the app loads without crashing
    expect(find.byType(MaterialApp), findsOneWidget);

    // The app should show either AuthScreen or HomeScreen
    // Since we're not authenticated, it should show AuthScreen
    expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
  });

  testWidgets('App shows loading indicator during initialization', (WidgetTester tester) async {
    // Build our app
    await tester.pumpWidget(const ProviderScope(child: PerkTrackerApp()));

    // Should show loading indicator initially
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
