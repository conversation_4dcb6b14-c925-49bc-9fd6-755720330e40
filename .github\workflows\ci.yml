name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  FLUTTER_VERSION: '3.24.5'

jobs:
  test:
    name: Test & Analyze
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .

      - name: Analyze project source
        run: flutter analyze --fatal-infos

      - name: Run tests
        run: flutter test --coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
          fail_ci_if_error: false

  build-web:
    name: Build Web
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Generate code
        run: dart run build_runner build --delete-conflicting-outputs

      - name: Build web
        run: flutter build web --release --web-renderer html

      - name: Upload web build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: web-build
          path: build/web/
          retention-days: 7

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, build-web]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download web build artifacts
        uses: actions/download-artifact@v4
        with:
          name: web-build
          path: build/web/

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Setup Firebase CLI
        run: |
          npm install -g firebase-tools

      - name: Deploy to Firebase Hosting (Staging)
        run: |
          firebase deploy --only hosting --project perktracker-staging --token $(gcloud auth print-access-token)

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, build-web]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download web build artifacts
        uses: actions/download-artifact@v4
        with:
          name: web-build
          path: build/web/

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Setup Firebase CLI
        run: |
          npm install -g firebase-tools

      - name: Deploy to Firebase Hosting (Production)
        run: |
          firebase deploy --only hosting --project perktracker-4c5be --token $(gcloud auth print-access-token)

  deploy-preview:
    name: Deploy Preview
    runs-on: ubuntu-latest
    needs: [test, build-web]
    if: github.event_name == 'pull_request'
    permissions:
      contents: read
      id-token: write
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download web build artifacts
        uses: actions/download-artifact@v4
        with:
          name: web-build
          path: build/web/

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: ${{ secrets.WIF_PROVIDER }}
          service_account: ${{ secrets.WIF_SERVICE_ACCOUNT }}

      - name: Setup Firebase CLI
        run: |
          npm install -g firebase-tools

      - name: Deploy to Firebase Hosting (Preview)
        run: |
          firebase hosting:channel:deploy pr-${{ github.event.number }} --project perktracker-4c5be --token $(gcloud auth print-access-token) --expires 7d
