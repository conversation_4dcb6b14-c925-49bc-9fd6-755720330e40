import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

/// Represents a user of the PerkTracker app
@JsonSerializable()
class User extends Equatable {
  /// Unique identifier for the user (Firebase UID)
  final String id;

  /// User's email address
  final String? email;

  /// User's display name
  final String? displayName;

  /// When the user account was created
  final DateTime createdAt;

  /// Last time the user was active
  final DateTime lastActiveAt;

  /// User preferences
  final UserPreferences preferences;

  /// Whether the user has completed onboarding
  final bool hasCompletedOnboarding;

  /// User's profile photo URL
  final String? photoUrl;

  const User({
    required this.id,
    this.email,
    this.displayName,
    required this.createdAt,
    required this.lastActiveAt,
    required this.preferences,
    this.hasCompletedOnboarding = false,
    this.photoUrl,
  });

  /// Create a new user with default preferences
  factory User.create({
    required String id,
    String? email,
    String? displayName,
    String? photoUrl,
  }) {
    final now = DateTime.now();
    return User(
      id: id,
      email: email,
      displayName: displayName,
      createdAt: now,
      lastActiveAt: now,
      preferences: UserPreferences.defaultPreferences(),
      hasCompletedOnboarding: false,
      photoUrl: photoUrl,
    );
  }

  /// Update last active timestamp
  User updateLastActive() {
    return copyWith(lastActiveAt: DateTime.now());
  }

  /// Mark onboarding as completed
  User completeOnboarding() {
    return copyWith(hasCompletedOnboarding: true);
  }

  /// Update user preferences
  User updatePreferences(UserPreferences newPreferences) {
    return copyWith(preferences: newPreferences);
  }

  /// Get display name or fallback to email
  String get effectiveDisplayName {
    if (displayName != null && displayName!.isNotEmpty) {
      return displayName!;
    }
    if (email != null && email!.isNotEmpty) {
      return email!.split('@').first;
    }
    return 'User';
  }

  /// Create a copy with updated values
  User copyWith({
    String? id,
    String? email,
    String? displayName,
    DateTime? createdAt,
    DateTime? lastActiveAt,
    UserPreferences? preferences,
    bool? hasCompletedOnboarding,
    String? photoUrl,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      preferences: preferences ?? this.preferences,
      hasCompletedOnboarding:
          hasCompletedOnboarding ?? this.hasCompletedOnboarding,
      photoUrl: photoUrl ?? this.photoUrl,
    );
  }

  /// JSON serialization
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        createdAt,
        lastActiveAt,
        preferences,
        hasCompletedOnboarding,
        photoUrl,
      ];

  @override
  String toString() {
    return 'User(id: $id, email: $email, displayName: $displayName)';
  }
}

/// User preferences and settings
@JsonSerializable()
class UserPreferences extends Equatable {
  /// Whether to show notifications for quarterly category changes
  final bool notifyQuarterlyChanges;

  /// Whether to show notifications for expiring promotions
  final bool notifyExpiringPromos;

  /// Days before expiration to show promo notifications
  final int promoNotificationDays;

  /// Whether to use dark theme
  final bool useDarkTheme;

  /// Default currency symbol
  final String currencySymbol;

  /// Whether to show annual fees in card comparisons
  final bool showAnnualFees;

  /// Whether to automatically activate quarterly categories
  final bool autoActivateQuarterly;

  /// Preferred sorting method for cards
  final CardSortMethod cardSortMethod;

  const UserPreferences({
    this.notifyQuarterlyChanges = true,
    this.notifyExpiringPromos = true,
    this.promoNotificationDays = 7,
    this.useDarkTheme = false,
    this.currencySymbol = '\$',
    this.showAnnualFees = true,
    this.autoActivateQuarterly = true,
    this.cardSortMethod = CardSortMethod.dateAdded,
  });

  /// Create default preferences
  factory UserPreferences.defaultPreferences() {
    return const UserPreferences();
  }

  /// Create a copy with updated values
  UserPreferences copyWith({
    bool? notifyQuarterlyChanges,
    bool? notifyExpiringPromos,
    int? promoNotificationDays,
    bool? useDarkTheme,
    String? currencySymbol,
    bool? showAnnualFees,
    bool? autoActivateQuarterly,
    CardSortMethod? cardSortMethod,
  }) {
    return UserPreferences(
      notifyQuarterlyChanges:
          notifyQuarterlyChanges ?? this.notifyQuarterlyChanges,
      notifyExpiringPromos: notifyExpiringPromos ?? this.notifyExpiringPromos,
      promoNotificationDays:
          promoNotificationDays ?? this.promoNotificationDays,
      useDarkTheme: useDarkTheme ?? this.useDarkTheme,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      showAnnualFees: showAnnualFees ?? this.showAnnualFees,
      autoActivateQuarterly:
          autoActivateQuarterly ?? this.autoActivateQuarterly,
      cardSortMethod: cardSortMethod ?? this.cardSortMethod,
    );
  }

  /// JSON serialization
  factory UserPreferences.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);

  @override
  List<Object?> get props => [
        notifyQuarterlyChanges,
        notifyExpiringPromos,
        promoNotificationDays,
        useDarkTheme,
        currencySymbol,
        showAnnualFees,
        autoActivateQuarterly,
        cardSortMethod,
      ];
}

/// Methods for sorting credit cards
enum CardSortMethod {
  @JsonValue('name')
  name('Name'),
  @JsonValue('issuer')
  issuer('Issuer'),
  @JsonValue('dateAdded')
  dateAdded('Date Added'),
  @JsonValue('rewardRate')
  rewardRate('Best Reward Rate');

  const CardSortMethod(this.displayName);
  final String displayName;
}
