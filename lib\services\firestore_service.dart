import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/credit_card.dart';
import '../models/user.dart';

/// Service for managing Firestore operations
class FirestoreService {
  static final FirestoreService _instance = FirestoreService._internal();
  factory FirestoreService() => _instance;
  FirestoreService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection references
  CollectionReference get _usersCollection => _firestore.collection('users');
  CollectionReference get _cardsCollection => _firestore.collection('cards');

  /// User operations

  /// Create or update user document
  Future<void> saveUser(User user) async {
    try {
      await _usersCollection.doc(user.id).set(user.toJson());
    } catch (e) {
      throw FirestoreException('Failed to save user: $e');
    }
  }

  /// Get user by ID
  Future<User?> getUser(String userId) async {
    try {
      final doc = await _usersCollection.doc(userId).get();
      if (doc.exists) {
        return User.fromJson(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      throw FirestoreException('Failed to get user: $e');
    }
  }

  /// Update user preferences
  Future<void> updateUserPreferences(
      String userId, UserPreferences preferences) async {
    try {
      await _usersCollection.doc(userId).update({
        'preferences': preferences.toJson(),
        'lastActiveAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw FirestoreException('Failed to update user preferences: $e');
    }
  }

  /// Update user last active timestamp
  Future<void> updateUserLastActive(String userId) async {
    try {
      await _usersCollection.doc(userId).update({
        'lastActiveAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw FirestoreException('Failed to update user last active: $e');
    }
  }

  /// Credit Card operations

  /// Save a credit card for a user
  Future<void> saveCreditCard(String userId, CreditCard card) async {
    try {
      final cardData = card.toJson();
      cardData['userId'] = userId;
      cardData['updatedAt'] = DateTime.now().toIso8601String();

      await _cardsCollection.doc(card.id).set(cardData);
    } catch (e) {
      throw FirestoreException('Failed to save credit card: $e');
    }
  }

  /// Get all credit cards for a user
  Future<List<CreditCard>> getUserCreditCards(String userId) async {
    try {
      final querySnapshot =
          await _cardsCollection.where('userId', isEqualTo: userId).get();

      final cards = querySnapshot.docs
          .map((doc) => CreditCard.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      // Sort in memory instead of using Firestore orderBy
      cards.sort((a, b) => b.dateAdded.compareTo(a.dateAdded));

      return cards;
    } catch (e) {
      throw FirestoreException('Failed to get user credit cards: $e');
    }
  }

  /// Get a specific credit card by ID
  Future<CreditCard?> getCreditCard(String cardId) async {
    try {
      final doc = await _cardsCollection.doc(cardId).get();
      if (doc.exists) {
        return CreditCard.fromJson(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      throw FirestoreException('Failed to get credit card: $e');
    }
  }

  /// Update a credit card
  Future<void> updateCreditCard(CreditCard card) async {
    try {
      final cardData = card.toJson();
      cardData['updatedAt'] = DateTime.now().toIso8601String();

      await _cardsCollection.doc(card.id).update(cardData);
    } catch (e) {
      throw FirestoreException('Failed to update credit card: $e');
    }
  }

  /// Delete a credit card
  Future<void> deleteCreditCard(String cardId) async {
    try {
      await _cardsCollection.doc(cardId).delete();
    } catch (e) {
      throw FirestoreException('Failed to delete credit card: $e');
    }
  }

  /// Stream user's credit cards for real-time updates
  Stream<List<CreditCard>> streamUserCreditCards(String userId) {
    try {
      return _cardsCollection
          .where('userId', isEqualTo: userId)
          .snapshots()
          .map((snapshot) {
        final cards = snapshot.docs
            .map((doc) =>
                CreditCard.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        // Sort in memory instead of using Firestore orderBy
        cards.sort((a, b) => b.dateAdded.compareTo(a.dateAdded));

        return cards;
      });
    } catch (e) {
      throw FirestoreException('Failed to stream user credit cards: $e');
    }
  }

  /// Stream user data for real-time updates
  Stream<User?> streamUser(String userId) {
    try {
      return _usersCollection.doc(userId).snapshots().map((doc) {
        if (doc.exists) {
          return User.fromJson(doc.data() as Map<String, dynamic>);
        }
        return null;
      });
    } catch (e) {
      throw FirestoreException('Failed to stream user: $e');
    }
  }

  /// Batch operations

  /// Save multiple credit cards in a batch
  Future<void> saveCreditCardsBatch(
      String userId, List<CreditCard> cards) async {
    try {
      final batch = _firestore.batch();
      final timestamp = DateTime.now().toIso8601String();

      for (final card in cards) {
        final cardData = card.toJson();
        cardData['userId'] = userId;
        cardData['updatedAt'] = timestamp;

        batch.set(_cardsCollection.doc(card.id), cardData);
      }

      await batch.commit();
    } catch (e) {
      throw FirestoreException('Failed to save credit cards batch: $e');
    }
  }

  /// Delete multiple credit cards in a batch
  Future<void> deleteCreditCardsBatch(List<String> cardIds) async {
    try {
      final batch = _firestore.batch();

      for (final cardId in cardIds) {
        batch.delete(_cardsCollection.doc(cardId));
      }

      await batch.commit();
    } catch (e) {
      throw FirestoreException('Failed to delete credit cards batch: $e');
    }
  }

  /// Utility methods

  /// Check if Firestore is available
  Future<bool> isAvailable() async {
    try {
      await _firestore.enableNetwork();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Clear all offline data
  Future<void> clearOfflineData() async {
    try {
      await _firestore.clearPersistence();
    } catch (e) {
      throw FirestoreException('Failed to clear offline data: $e');
    }
  }
}

/// Custom exception for Firestore operations
class FirestoreException implements Exception {
  final String message;
  const FirestoreException(this.message);

  @override
  String toString() => 'FirestoreException: $message';
}
