// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reward.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Reward _$RewardFromJson(Map<String, dynamic> json) => Reward(
      category: $enumDecode(_$RewardCategoryEnumMap, json['category']),
      rate: (json['rate'] as num).toDouble(),
      type: $enumDecode(_$RewardTypeEnumMap, json['type']),
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      spendingCap: (json['spendingCap'] as num?)?.toDouble(),
      isPercentage: json['isPercentage'] as bool? ?? true,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$RewardToJson(Reward instance) => <String, dynamic>{
      'category': _$RewardCategoryEnumMap[instance.category]!,
      'rate': instance.rate,
      'type': _$RewardTypeEnumMap[instance.type]!,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'spendingCap': instance.spendingCap,
      'isPercentage': instance.isPercentage,
      'notes': instance.notes,
    };

const _$RewardCategoryEnumMap = {
  RewardCategory.groceries: 'groceries',
  RewardCategory.gas: 'gas',
  RewardCategory.dining: 'dining',
  RewardCategory.travel: 'travel',
  RewardCategory.streaming: 'streaming',
  RewardCategory.onlineShopping: 'onlineShopping',
  RewardCategory.drugstores: 'drugstores',
  RewardCategory.transit: 'transit',
  RewardCategory.utilities: 'utilities',
  RewardCategory.wholesale: 'wholesale',
  RewardCategory.homeImprovement: 'homeImprovement',
  RewardCategory.department: 'department',
  RewardCategory.entertainment: 'entertainment',
  RewardCategory.cellular: 'cellular',
  RewardCategory.internet: 'internet',
  RewardCategory.cable: 'cable',
  RewardCategory.other: 'other',
};

const _$RewardTypeEnumMap = {
  RewardType.static: 'static',
  RewardType.quarterly: 'quarterly',
  RewardType.promo: 'promo',
};
