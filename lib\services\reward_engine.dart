import '../models/credit_card.dart';
import '../models/reward.dart';
import '../utils/categories.dart';

/// Engine for finding the best credit cards for specific purchases
class RewardEngine {
  static final RewardEngine _instance = RewardEngine._internal();
  factory RewardEngine() => _instance;
  RewardEngine._internal();

  /// Find the best credit card for a specific category
  CardRecommendation? getBestCardForCategory({
    required List<CreditCard> cards,
    required RewardCategory category,
    double? purchaseAmount,
    bool includeInactiveCards = false,
  }) {
    final eligibleCards = cards.where((card) {
      return includeInactiveCards || card.isActive;
    }).toList();

    if (eligibleCards.isEmpty) return null;

    final cardScores = <MapEntry<CreditCard, CardScore>>[];

    for (final card in eligibleCards) {
      final score = _calculateCardScore(card, category, purchaseAmount);
      cardScores.add(MapEntry(card, score));
    }

    // Sort by total value (highest first)
    cardScores.sort((a, b) => b.value.totalValue.compareTo(a.value.totalValue));

    final bestCard = cardScores.first;
    return CardRecommendation(
      card: bestCard.key,
      category: category,
      rewardRate: bestCard.value.rewardRate,
      estimatedValue: bestCard.value.totalValue,
      applicableReward: bestCard.value.applicableReward,
      purchaseAmount: purchaseAmount,
    );
  }

  /// Find the best credit cards for multiple categories
  List<CardRecommendation> getBestCardsForCategories({
    required List<CreditCard> cards,
    required List<RewardCategory> categories,
    double? purchaseAmount,
    bool includeInactiveCards = false,
  }) {
    final recommendations = <CardRecommendation>[];

    for (final category in categories) {
      final recommendation = getBestCardForCategory(
        cards: cards,
        category: category,
        purchaseAmount: purchaseAmount,
        includeInactiveCards: includeInactiveCards,
      );

      if (recommendation != null) {
        recommendations.add(recommendation);
      }
    }

    return recommendations;
  }

  /// Find the best card for a merchant name
  CardRecommendation? getBestCardForMerchant({
    required List<CreditCard> cards,
    required String merchantName,
    double? purchaseAmount,
    bool includeInactiveCards = false,
  }) {
    final category = MerchantCategories.getCategoryForMerchant(merchantName);
    if (category == null) {
      // If we can't determine the category, return the card with the highest default rate
      return _getBestDefaultCard(cards, purchaseAmount, includeInactiveCards);
    }

    return getBestCardForCategory(
      cards: cards,
      category: category,
      purchaseAmount: purchaseAmount,
      includeInactiveCards: includeInactiveCards,
    );
  }

  /// Get top N cards for a category
  List<CardRecommendation> getTopCardsForCategory({
    required List<CreditCard> cards,
    required RewardCategory category,
    int limit = 3,
    double? purchaseAmount,
    bool includeInactiveCards = false,
  }) {
    final eligibleCards = cards.where((card) {
      return includeInactiveCards || card.isActive;
    }).toList();

    if (eligibleCards.isEmpty) return [];

    final cardScores = <MapEntry<CreditCard, CardScore>>[];

    for (final card in eligibleCards) {
      final score = _calculateCardScore(card, category, purchaseAmount);
      cardScores.add(MapEntry(card, score));
    }

    // Sort by total value (highest first)
    cardScores.sort((a, b) => b.value.totalValue.compareTo(a.value.totalValue));

    final recommendations = <CardRecommendation>[];
    final limitedCards = cardScores.take(limit);

    for (final entry in limitedCards) {
      recommendations.add(CardRecommendation(
        card: entry.key,
        category: category,
        rewardRate: entry.value.rewardRate,
        estimatedValue: entry.value.totalValue,
        applicableReward: entry.value.applicableReward,
        purchaseAmount: purchaseAmount,
      ));
    }

    return recommendations;
  }

  /// Calculate comprehensive card score for a category
  CardScore _calculateCardScore(CreditCard card, RewardCategory category, double? purchaseAmount) {
    final bestReward = card.getBestRewardForCategory(category);
    final effectiveRate = card.getEffectiveRewardRate(category);

    double baseValue = 0;
    if (purchaseAmount != null) {
      baseValue = purchaseAmount * effectiveRate;
    }

    // Apply penalties for annual fees
    double adjustedValue = baseValue;
    if (card.annualFee != null && card.annualFee! > 0) {
      // Amortize annual fee over 12 months for monthly value calculation
      final monthlyFee = card.annualFee! / 12;
      adjustedValue = baseValue - monthlyFee;
    }

    return CardScore(
      rewardRate: effectiveRate,
      baseValue: baseValue,
      totalValue: adjustedValue,
      applicableReward: bestReward,
    );
  }

  /// Get the card with the best default rate when category is unknown
  CardRecommendation? _getBestDefaultCard(List<CreditCard> cards, double? purchaseAmount, bool includeInactiveCards) {
    final eligibleCards = cards.where((card) {
      return includeInactiveCards || card.isActive;
    }).toList();

    if (eligibleCards.isEmpty) return null;

    // Sort by default reward rate
    eligibleCards.sort((a, b) => b.defaultRewardRate.compareTo(a.defaultRewardRate));

    final bestCard = eligibleCards.first;
    double estimatedValue = 0;
    if (purchaseAmount != null) {
      estimatedValue = purchaseAmount * bestCard.defaultRewardRate;
    }

    return CardRecommendation(
      card: bestCard,
      category: RewardCategory.other,
      rewardRate: bestCard.defaultRewardRate,
      estimatedValue: estimatedValue,
      applicableReward: null,
      purchaseAmount: purchaseAmount,
    );
  }

  /// Get cards with active quarterly bonuses
  List<CreditCard> getCardsWithActiveQuarterlyBonuses(List<CreditCard> cards) {
    return cards.where((card) => card.currentQuarterlyRewards.isNotEmpty).toList();
  }

  /// Get cards with active promotional offers
  List<CreditCard> getCardsWithActivePromotions(List<CreditCard> cards) {
    return cards.where((card) => card.currentPromotionalRewards.isNotEmpty).toList();
  }

  /// Get all unique categories covered by user's cards
  Set<RewardCategory> getCoveredCategories(List<CreditCard> cards) {
    final categories = <RewardCategory>{};
    for (final card in cards) {
      categories.addAll(card.rewardCategories);
    }
    return categories;
  }

  /// Get categories that are not well covered (low reward rates)
  List<RewardCategory> getUncoveredCategories(List<CreditCard> cards, {double minimumRate = 0.02}) {
    final uncovered = <RewardCategory>[];

    for (final category in RewardCategory.values) {
      final bestCard = getBestCardForCategory(
        cards: cards,
        category: category,
      );

      if (bestCard == null || bestCard.rewardRate < minimumRate) {
        uncovered.add(category);
      }
    }

    return uncovered;
  }
}

/// Represents a card recommendation for a specific category
class CardRecommendation {
  final CreditCard card;
  final RewardCategory category;
  final double rewardRate;
  final double estimatedValue;
  final Reward? applicableReward;
  final double? purchaseAmount;

  const CardRecommendation({
    required this.card,
    required this.category,
    required this.rewardRate,
    required this.estimatedValue,
    this.applicableReward,
    this.purchaseAmount,
  });

  /// Get formatted reward rate for display
  String get formattedRewardRate {
    return card.getFormattedRewardRate(category);
  }

  /// Get formatted estimated value for display
  String get formattedEstimatedValue {
    return '\$${estimatedValue.toStringAsFixed(2)}';
  }

  /// Check if this recommendation has a time-limited offer
  bool get hasTimeLimitedOffer {
    return applicableReward?.type == RewardType.promo ||
           applicableReward?.type == RewardType.quarterly;
  }

  /// Get days remaining for time-limited offers
  int? get daysRemaining {
    return applicableReward?.daysRemaining;
  }
}

/// Internal scoring data for cards
class CardScore {
  final double rewardRate;
  final double baseValue;
  final double totalValue;
  final Reward? applicableReward;

  const CardScore({
    required this.rewardRate,
    required this.baseValue,
    required this.totalValue,
    this.applicableReward,
  });
}
