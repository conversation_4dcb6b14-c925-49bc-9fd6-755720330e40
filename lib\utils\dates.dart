import 'package:intl/intl.dart';

/// Date utilities for quarterly rewards and promo tracking
class DateUtils {
  static final DateFormat _monthDayFormat = DateFormat('MMM d');
  static final DateFormat _fullDateFormat = DateFormat('MMM d, yyyy');
  static final DateFormat _shortDateFormat = DateFormat('M/d/yy');

  /// Get current quarter (1-4)
  static int getCurrentQuarter([DateTime? date]) {
    final targetDate = date ?? DateTime.now();
    return ((targetDate.month - 1) ~/ 3) + 1;
  }

  /// Get quarter for a specific date
  static int getQuarter(DateTime date) {
    return ((date.month - 1) ~/ 3) + 1;
  }

  /// Get start date of a quarter
  static DateTime getQuarterStart(int year, int quarter) {
    final month = (quarter - 1) * 3 + 1;
    return DateTime(year, month, 1);
  }

  /// Get end date of a quarter
  static DateTime getQuarterEnd(int year, int quarter) {
    final month = quarter * 3;
    final nextMonth = month == 12 ? 1 : month + 1;
    final nextYear = month == 12 ? year + 1 : year;
    return DateTime(nextYear, nextMonth, 1).subtract(const Duration(days: 1));
  }

  /// Get current quarter start date
  static DateTime getCurrentQuarterStart([DateTime? date]) {
    final targetDate = date ?? DateTime.now();
    final quarter = getCurrentQuarter(targetDate);
    return getQuarterStart(targetDate.year, quarter);
  }

  /// Get current quarter end date
  static DateTime getCurrentQuarterEnd([DateTime? date]) {
    final targetDate = date ?? DateTime.now();
    final quarter = getCurrentQuarter(targetDate);
    return getQuarterEnd(targetDate.year, quarter);
  }

  /// Get next quarter start date
  static DateTime getNextQuarterStart([DateTime? date]) {
    final targetDate = date ?? DateTime.now();
    final currentQuarter = getCurrentQuarter(targetDate);
    final nextQuarter = currentQuarter == 4 ? 1 : currentQuarter + 1;
    final nextYear =
        currentQuarter == 4 ? targetDate.year + 1 : targetDate.year;
    return getQuarterStart(nextYear, nextQuarter);
  }

  /// Check if a date is in the current quarter
  static bool isInCurrentQuarter(DateTime date, [DateTime? referenceDate]) {
    final reference = referenceDate ?? DateTime.now();
    final quarterStart = getCurrentQuarterStart(reference);
    final quarterEnd = getCurrentQuarterEnd(reference);
    return date.isAfter(quarterStart.subtract(const Duration(days: 1))) &&
        date.isBefore(quarterEnd.add(const Duration(days: 1)));
  }

  /// Check if a promo is currently active
  static bool isPromoActive(DateTime? startDate, DateTime? endDate,
      [DateTime? referenceDate]) {
    final now = referenceDate ?? DateTime.now();

    if (startDate != null && now.isBefore(startDate)) {
      return false;
    }

    if (endDate != null && now.isAfter(endDate)) {
      return false;
    }

    return true;
  }

  /// Get days remaining in current quarter
  static int getDaysRemainingInQuarter([DateTime? date]) {
    final targetDate = date ?? DateTime.now();
    final quarterEnd = getCurrentQuarterEnd(targetDate);
    return quarterEnd.difference(targetDate).inDays;
  }

  /// Get days remaining for a promo
  static int? getDaysRemainingForPromo(DateTime? endDate,
      [DateTime? referenceDate]) {
    if (endDate == null) return null;
    final now = referenceDate ?? DateTime.now();
    final difference = endDate.difference(now).inDays;
    return difference >= 0 ? difference : 0;
  }

  /// Format date for display
  static String formatDate(DateTime date, {bool includeYear = false}) {
    if (includeYear) {
      return _fullDateFormat.format(date);
    } else {
      return _monthDayFormat.format(date);
    }
  }

  /// Format date range for display
  static String formatDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate == null && endDate == null) {
      return 'No expiration';
    }

    if (startDate == null) {
      return 'Until ${formatDate(endDate!)}';
    }

    if (endDate == null) {
      return 'From ${formatDate(startDate)}';
    }

    final sameYear = startDate.year == endDate.year;
    final startFormatted = formatDate(startDate, includeYear: !sameYear);
    final endFormatted = formatDate(endDate, includeYear: true);

    return '$startFormatted - $endFormatted';
  }

  /// Get quarter name (Q1, Q2, Q3, Q4)
  static String getQuarterName(int quarter) {
    return 'Q$quarter';
  }

  /// Get quarter display name with year
  static String getQuarterDisplayName(int year, int quarter) {
    return '${getQuarterName(quarter)} $year';
  }

  /// Get current quarter display name
  static String getCurrentQuarterDisplayName([DateTime? date]) {
    final targetDate = date ?? DateTime.now();
    final quarter = getCurrentQuarter(targetDate);
    return getQuarterDisplayName(targetDate.year, quarter);
  }

  /// Check if two dates are in the same quarter
  static bool isSameQuarter(DateTime date1, DateTime date2) {
    return date1.year == date2.year && getQuarter(date1) == getQuarter(date2);
  }

  /// Get short date format for compact display
  static String formatShortDate(DateTime date) {
    return _shortDateFormat.format(date);
  }

  /// Parse date from string (flexible format)
  static DateTime? parseDate(String dateString) {
    try {
      // Try common formats
      final formats = [
        DateFormat('yyyy-MM-dd'),
        DateFormat('MM/dd/yyyy'),
        DateFormat('M/d/yyyy'),
        DateFormat('MMM d, yyyy'),
        DateFormat('MMM d yyyy'),
      ];

      for (final format in formats) {
        try {
          return format.parse(dateString);
        } catch (_) {
          continue;
        }
      }

      // Fallback to DateTime.parse
      return DateTime.parse(dateString);
    } catch (_) {
      return null;
    }
  }
}
