import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/credit_card.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';

/// Screen for adding a new credit card with beautiful design
class AddCardScreen extends ConsumerStatefulWidget {
  const AddCardScreen({super.key});

  @override
  ConsumerState<AddCardScreen> createState() => _AddCardScreenState();
}

class _AddCardScreenState extends ConsumerState<AddCardScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _issuerController = TextEditingController();
  final _lastFourController = TextEditingController();
  final _annualFeeController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  String? _errorMessage;

  // Animation controllers
  late AnimationController _cardAnimationController;
  late AnimationController _formAnimationController;
  late Animation<double> _cardAnimation;
  late Animation<Offset> _formAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _formAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _cardAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _cardAnimationController, curve: Curves.elasticOut),
    );
    _formAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(
          parent: _formAnimationController, curve: Curves.easeOutCubic),
    );

    // Start animations
    _cardAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _formAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _issuerController.dispose();
    _lastFourController.dispose();
    _annualFeeController.dispose();
    _notesController.dispose();
    _cardAnimationController.dispose();
    _formAnimationController.dispose();
    super.dispose();
  }

  // Get issuer color based on name - using PerkTracker brand colors
  Color _getIssuerColor(String issuer) {
    switch (issuer.toLowerCase()) {
      case 'chase':
        return const Color(0xFF0066B2);
      case 'american express':
      case 'amex':
        return const Color(0xFF006FCF);
      case 'capital one':
        return const Color(0xFFDB0011);
      case 'discover':
        return const Color(0xFFFF6000);
      case 'citi':
      case 'citibank':
        return const Color(0xFF1976D2);
      case 'wells fargo':
        return const Color(0xFFD71921);
      case 'bank of america':
        return const Color(0xFFE31837);
      default:
        return const Color(0xFF2D7D7D); // PerkTracker brand teal
    }
  }

  // Save card to Firestore
  Future<void> _saveCard() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = AuthService();
      final user = await authService.getCurrentAppUser();
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final card = CreditCard.create(
        name: _nameController.text.trim(),
        issuer: _issuerController.text.trim(),
        lastFourDigits: _lastFourController.text.trim(),
        annualFee: double.tryParse(_annualFeeController.text) ?? 0.0,
        notes: _notesController.text.trim(),
        rewards: [], // Will be added in next phase
      );

      final firestoreService = FirestoreService();
      await firestoreService.saveCreditCard(user.id, card);

      if (mounted) {
        // Show success animation
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('${card.name} added successfully!'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );

        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to save card: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F9F9), // Light teal background
      appBar: AppBar(
        title: const Text(
          'Add Credit Card',
          style:
              TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF2D7D7D)),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF2D7D7D), // PerkTracker teal
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF2D7D7D)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Animated Card Preview
            AnimatedBuilder(
              animation: _cardAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _cardAnimation.value,
                  child: Container(
                    margin: const EdgeInsets.all(20),
                    height: 200,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          _getIssuerColor(_issuerController.text),
                          _getIssuerColor(_issuerController.text)
                              .withValues(alpha: 0.8),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: _getIssuerColor(_issuerController.text)
                              .withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _issuerController.text.isEmpty
                                    ? 'Bank Name'
                                    : _issuerController.text,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Text(
                                  'CREDIT',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Text(
                            _nameController.text.isEmpty
                                ? 'Card Name'
                                : _nameController.text,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _lastFourController.text.isEmpty
                                    ? '•••• •••• •••• ••••'
                                    : '•••• •••• •••• ${_lastFourController.text}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  letterSpacing: 2,
                                ),
                              ),
                              const Icon(
                                Icons.contactless,
                                color: Colors.white,
                                size: 24,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),

            // Animated Form
            Expanded(
              child: SlideTransition(
                position: _formAnimation,
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                  ),
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      padding: const EdgeInsets.all(24),
                      children: [
                        // Form Title
                        const Text(
                          'Card Details',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Error Message
                        if (_errorMessage != null)
                          Container(
                            padding: const EdgeInsets.all(12),
                            margin: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.red.shade200),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.error_outline,
                                    color: Colors.red.shade600),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _errorMessage!,
                                    style:
                                        TextStyle(color: Colors.red.shade600),
                                  ),
                                ),
                              ],
                            ),
                          ),

                        // Card Name Field
                        _buildTextField(
                          controller: _nameController,
                          label: 'Card Name',
                          hint: 'e.g., Chase Sapphire Preferred',
                          icon: Icons.credit_card,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter a card name';
                            }
                            return null;
                          },
                          onChanged: (value) => setState(() {}),
                        ),

                        const SizedBox(height: 20),

                        // Issuer Field
                        _buildTextField(
                          controller: _issuerController,
                          label: 'Bank/Issuer',
                          hint: 'e.g., Chase, American Express',
                          icon: Icons.account_balance,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter the bank or issuer';
                            }
                            return null;
                          },
                          onChanged: (value) => setState(() {}),
                        ),

                        const SizedBox(height: 20),

                        // Last Four Digits
                        _buildTextField(
                          controller: _lastFourController,
                          label: 'Last 4 Digits',
                          hint: '1234',
                          icon: Icons.numbers,
                          keyboardType: TextInputType.number,
                          maxLength: 4,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter the last 4 digits';
                            }
                            if (value.length != 4) {
                              return 'Please enter exactly 4 digits';
                            }
                            return null;
                          },
                          onChanged: (value) => setState(() {}),
                        ),

                        const SizedBox(height: 20),

                        // Annual Fee Field
                        _buildTextField(
                          controller: _annualFeeController,
                          label: 'Annual Fee',
                          hint: '0',
                          icon: Icons.attach_money,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              final fee = double.tryParse(value);
                              if (fee == null || fee < 0) {
                                return 'Please enter a valid fee amount';
                              }
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 20),

                        // Notes Field
                        _buildTextField(
                          controller: _notesController,
                          label: 'Notes (Optional)',
                          hint: 'Any additional notes about this card',
                          icon: Icons.note,
                          maxLines: 3,
                        ),

                        const SizedBox(height: 32),

                        // Save Button
                        SizedBox(
                          width: double.infinity,
                          height: 56,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _saveCard,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  const Color(0xFFFF8C42), // PerkTracker orange
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                              elevation: 8,
                              shadowColor: const Color(0xFFFF8C42)
                                  .withValues(alpha: 0.3),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    ),
                                  )
                                : const Text(
                                    'Add Card',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build consistent text fields
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int? maxLength,
    int maxLines = 1,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLength: maxLength,
          maxLines: maxLines,
          validator: validator,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon:
                Icon(icon, color: const Color(0xFF2D7D7D)), // PerkTracker teal
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0F2F1), width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0F2F1), width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                  color: Color(0xFF2D7D7D), width: 2), // PerkTracker teal
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            counterText: maxLength != null ? '' : null,
          ),
        ),
      ],
    );
  }
}
