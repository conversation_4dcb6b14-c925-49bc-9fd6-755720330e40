// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyB70PlWmWTR5C1UUCixbH5EZEyzJwJKudc',
    appId: '1:801180332155:web:0ff615adfb91dafad532de',
    messagingSenderId: '801180332155',
    projectId: 'perktracker-4c5be',
    authDomain: 'perktracker-4c5be.firebaseapp.com',
    storageBucket: 'perktracker-4c5be.firebasestorage.app',
    measurementId: 'G-888CNSMCME',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBFRzAzn9ZtvYxHo2-odl8PrI0I0AkRje4',
    appId: '1:801180332155:android:6510457b88cf9237d532de',
    messagingSenderId: '801180332155',
    projectId: 'perktracker-4c5be',
    storageBucket: 'perktracker-4c5be.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCYTSEwFtofquZnSn1mE7Ct5ma0_kwyWEo',
    appId: '1:801180332155:ios:30a15e60af91423ad532de',
    messagingSenderId: '801180332155',
    projectId: 'perktracker-4c5be',
    storageBucket: 'perktracker-4c5be.firebasestorage.app',
    iosBundleId: 'com.example.perkTracker',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCYTSEwFtofquZnSn1mE7Ct5ma0_kwyWEo',
    appId: '1:801180332155:ios:30a15e60af91423ad532de',
    messagingSenderId: '801180332155',
    projectId: 'perktracker-4c5be',
    storageBucket: 'perktracker-4c5be.firebasestorage.app',
    iosBundleId: 'com.example.perkTracker',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyB70PlWmWTR5C1UUCixbH5EZEyzJwJKudc',
    appId: '1:801180332155:web:740dc3b7faf61328d532de',
    messagingSenderId: '801180332155',
    projectId: 'perktracker-4c5be',
    authDomain: 'perktracker-4c5be.firebaseapp.com',
    storageBucket: 'perktracker-4c5be.firebasestorage.app',
    measurementId: 'G-BY11CPRZS5',
  );
}
