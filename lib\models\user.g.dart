// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  email: json['email'] as String?,
  displayName: json['displayName'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  lastActiveAt: DateTime.parse(json['lastActiveAt'] as String),
  preferences: UserPreferences.fromJson(
    json['preferences'] as Map<String, dynamic>,
  ),
  hasCompletedOnboarding: json['hasCompletedOnboarding'] as bool? ?? false,
  photoUrl: json['photoUrl'] as String?,
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'email': instance.email,
  'displayName': instance.displayName,
  'createdAt': instance.createdAt.toIso8601String(),
  'lastActiveAt': instance.lastActiveAt.toIso8601String(),
  'preferences': instance.preferences,
  'hasCompletedOnboarding': instance.hasCompletedOnboarding,
  'photoUrl': instance.photoUrl,
};

UserPreferences _$UserPreferencesFromJson(
  Map<String, dynamic> json,
) => UserPreferences(
  notifyQuarterlyChanges: json['notifyQuarterlyChanges'] as bool? ?? true,
  notifyExpiringPromos: json['notifyExpiringPromos'] as bool? ?? true,
  promoNotificationDays: (json['promoNotificationDays'] as num?)?.toInt() ?? 7,
  useDarkTheme: json['useDarkTheme'] as bool? ?? false,
  currencySymbol: json['currencySymbol'] as String? ?? '\$',
  showAnnualFees: json['showAnnualFees'] as bool? ?? true,
  autoActivateQuarterly: json['autoActivateQuarterly'] as bool? ?? true,
  cardSortMethod:
      $enumDecodeNullable(_$CardSortMethodEnumMap, json['cardSortMethod']) ??
      CardSortMethod.dateAdded,
);

Map<String, dynamic> _$UserPreferencesToJson(UserPreferences instance) =>
    <String, dynamic>{
      'notifyQuarterlyChanges': instance.notifyQuarterlyChanges,
      'notifyExpiringPromos': instance.notifyExpiringPromos,
      'promoNotificationDays': instance.promoNotificationDays,
      'useDarkTheme': instance.useDarkTheme,
      'currencySymbol': instance.currencySymbol,
      'showAnnualFees': instance.showAnnualFees,
      'autoActivateQuarterly': instance.autoActivateQuarterly,
      'cardSortMethod': _$CardSortMethodEnumMap[instance.cardSortMethod]!,
    };

const _$CardSortMethodEnumMap = {
  CardSortMethod.name: 'name',
  CardSortMethod.issuer: 'issuer',
  CardSortMethod.dateAdded: 'dateAdded',
  CardSortMethod.rewardRate: 'rewardRate',
};
