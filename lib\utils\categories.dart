/// Credit card reward categories and related utilities
enum RewardCategory {
  groceries('Groceries'),
  gas('Gas'),
  dining('Dining'),
  travel('Travel'),
  streaming('Streaming'),
  onlineShopping('Online Shopping'),
  drugstores('Drugstores'),
  transit('Transit'),
  utilities('Utilities'),
  wholesale('Wholesale Clubs'),
  homeImprovement('Home Improvement'),
  department('Department Stores'),
  entertainment('Entertainment'),
  cellular('Cellular'),
  internet('Internet'),
  cable('Cable'),
  other('Other');

  const RewardCategory(this.displayName);
  final String displayName;

  /// Get category from string (case-insensitive)
  static RewardCategory? fromString(String value) {
    final normalized = value.toLowerCase().trim();
    for (final category in RewardCategory.values) {
      if (category.name.toLowerCase() == normalized ||
          category.displayName.toLowerCase() == normalized) {
        return category;
      }
    }
    return null;
  }

  /// Get all category names for dropdowns
  static List<String> get allDisplayNames =>
      RewardCategory.values.map((e) => e.displayName).toList();
}

/// Quarterly rotating categories for major issuers
class QuarterlyCategories {
  static const Map<String, Map<int, List<RewardCategory>>> _quarterlyRotations = {
    'Chase Freedom': {
      1: [RewardCategory.groceries, RewardCategory.drugstores],
      2: [RewardCategory.gas, RewardCategory.homeImprovement],
      3: [RewardCategory.onlineShopping],
      4: [RewardCategory.wholesale, RewardCategory.department],
    },
    'Discover It': {
      1: [RewardCategory.groceries, RewardCategory.drugstores],
      2: [RewardCategory.gas, RewardCategory.homeImprovement],
      3: [RewardCategory.onlineShopping],
      4: [RewardCategory.wholesale, RewardCategory.department],
    },
    'Chase Freedom Flex': {
      1: [RewardCategory.groceries, RewardCategory.drugstores],
      2: [RewardCategory.gas, RewardCategory.homeImprovement],
      3: [RewardCategory.onlineShopping],
      4: [RewardCategory.wholesale, RewardCategory.department],
    },
  };

  /// Get current quarter (1-4)
  static int getCurrentQuarter() {
    final now = DateTime.now();
    return ((now.month - 1) ~/ 3) + 1;
  }

  /// Get categories for a specific card and quarter
  static List<RewardCategory> getCategoriesForQuarter(String cardName, int quarter) {
    return _quarterlyRotations[cardName]?[quarter] ?? [];
  }

  /// Get current active categories for a card
  static List<RewardCategory> getCurrentCategories(String cardName) {
    return getCategoriesForQuarter(cardName, getCurrentQuarter());
  }

  /// Check if a card has quarterly rotations
  static bool hasQuarterlyRotation(String cardName) {
    return _quarterlyRotations.containsKey(cardName);
  }

  /// Get all cards with quarterly rotations
  static List<String> get cardsWithRotations => _quarterlyRotations.keys.toList();
}

/// Common merchant mappings to categories
class MerchantCategories {
  static const Map<String, RewardCategory> _merchantMappings = {
    // Groceries
    'walmart': RewardCategory.groceries,
    'target': RewardCategory.groceries,
    'kroger': RewardCategory.groceries,
    'safeway': RewardCategory.groceries,
    'whole foods': RewardCategory.groceries,
    'trader joes': RewardCategory.groceries,
    'costco': RewardCategory.wholesale,
    'sams club': RewardCategory.wholesale,
    'bjs': RewardCategory.wholesale,
    
    // Gas
    'shell': RewardCategory.gas,
    'exxon': RewardCategory.gas,
    'bp': RewardCategory.gas,
    'chevron': RewardCategory.gas,
    'mobil': RewardCategory.gas,
    'texaco': RewardCategory.gas,
    
    // Dining
    'mcdonalds': RewardCategory.dining,
    'starbucks': RewardCategory.dining,
    'subway': RewardCategory.dining,
    'chipotle': RewardCategory.dining,
    'dominos': RewardCategory.dining,
    'pizza hut': RewardCategory.dining,
    
    // Streaming
    'netflix': RewardCategory.streaming,
    'hulu': RewardCategory.streaming,
    'disney plus': RewardCategory.streaming,
    'amazon prime': RewardCategory.streaming,
    'spotify': RewardCategory.streaming,
    'apple music': RewardCategory.streaming,
    
    // Travel
    'delta': RewardCategory.travel,
    'american airlines': RewardCategory.travel,
    'united': RewardCategory.travel,
    'southwest': RewardCategory.travel,
    'marriott': RewardCategory.travel,
    'hilton': RewardCategory.travel,
    'hyatt': RewardCategory.travel,
    
    // Home Improvement
    'home depot': RewardCategory.homeImprovement,
    'lowes': RewardCategory.homeImprovement,
    'menards': RewardCategory.homeImprovement,
    
    // Department Stores
    'macys': RewardCategory.department,
    'nordstrom': RewardCategory.department,
    'kohls': RewardCategory.department,
    'jcpenney': RewardCategory.department,
  };

  /// Get category for a merchant name
  static RewardCategory? getCategoryForMerchant(String merchantName) {
    final normalized = merchantName.toLowerCase().trim();
    
    // Direct match
    if (_merchantMappings.containsKey(normalized)) {
      return _merchantMappings[normalized];
    }
    
    // Partial match
    for (final entry in _merchantMappings.entries) {
      if (normalized.contains(entry.key) || entry.key.contains(normalized)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Get all known merchants for a category
  static List<String> getMerchantsForCategory(RewardCategory category) {
    return _merchantMappings.entries
        .where((entry) => entry.value == category)
        .map((entry) => entry.key)
        .toList();
  }
}
